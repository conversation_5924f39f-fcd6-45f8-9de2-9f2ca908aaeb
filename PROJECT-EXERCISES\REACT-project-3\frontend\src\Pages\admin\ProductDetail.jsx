import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import {
  asyncDeleteProducts,
  asyncUpdateProducts,
  asyncLoadProducts,
} from "../../store/Actions/productAction";

import { useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { useEffect } from "react";
import { asyncUpdateUser } from "../../store/Actions/UserAction";

const ProductDetail = () => {
  const { id } = useParams();
  const products = useSelector((state) => state.products.data);
  const users = useSelector((state) => state.users.data);
  const product = products?.find((product) => product.id == id);
  console.log(products, users);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  useEffect(() => {
    dispatch(asyncLoadProducts());
  }, [dispatch]);
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      image: product?.image,
      title: product?.title,
      price: product?.price,
      category: product?.category,
      description: product?.description,
    },
  });

  const updateProductHandler = (product) => {
    dispatch(asyncUpdateProducts(id, product));
    navigate("/products");
  };

  const deleteHandler = (id) => {
    dispatch(asyncDeleteProducts(id));
    navigate("/products");
  };
  const addToCarHandler = (id) => {
    // Check if users exists and has a cart property
    if (!users || !users.cart) {
      console.error("Users or users.cart is undefined");
      return;
    }
    
    // Create a deep copy of the users object with the cart
    const copyUser = {...users, cart: Array.isArray(users.cart) ? [...users.cart] : []};
    
    // Find the existing item in the cart
    const existingItemIndex = copyUser.cart.findIndex(c => c.productid === id);
    
    if(existingItemIndex === -1){
      copyUser.cart.push({
        productid: id,
        quantity: 1
      });
    }
    else{
      copyUser.cart[existingItemIndex] = {
        productid: id,
        quantity: copyUser.cart[existingItemIndex].quantity + 1,
      };
    }
    
    dispatch(asyncUpdateUser(copyUser.id, copyUser));
  }

  return product ? (
    <div className="space-y-8">
      {/* Product Display */}
      <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/20 rounded-2xl overflow-hidden shadow-2xl">
        <div className="flex flex-col lg:flex-row">
          {/* Product Image */}
          <div className="lg:w-1/2 relative">
            <img
              className="w-full h-[400px] lg:h-[500px] object-cover"
              src={product.image}
              alt={product.title}
            />
            <div className="absolute top-4 right-4">
              <span className="px-3 py-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-sm font-semibold rounded-full shadow-lg">
                {product.category}
              </span>
            </div>
          </div>
          
          {/* Product Info */}
          <div className="lg:w-1/2 p-8">
            <h1 className="text-4xl font-bold text-white mb-4 leading-tight">
              {product.title}
            </h1>
            
            <p className="text-gray-300 text-lg mb-6 leading-relaxed">
              {product.description}
            </p>
            
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center gap-4">
                <span className="text-4xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
                  ${product.price}
                </span>
              </div>
            </div>
            
            {/* Action Button */}
            <button 
              onClick={() => addToCarHandler(product.id)}
              className="w-full py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-500 hover:to-purple-500 transform hover:scale-105 transition-all duration-300 shadow-2xl flex items-center justify-center gap-3"
            >
              <span className="text-xl">🛒</span>
              Add to Cart
            </button>
          </div>
        </div>
      </div>
      
      {/* Admin Edit Section */}
      {users && users.isAdmin && (
        <div className="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 backdrop-blur-sm border border-yellow-400/30 rounded-2xl p-8">
          <div className="text-center mb-6">
            <h2 className="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent mb-2">
              ⚙️ Admin Panel
            </h2>
            <p className="text-gray-300">Edit or manage this product</p>
          </div>
          
          <form onSubmit={handleSubmit(updateProductHandler)} className="space-y-6">
            {/* Product Title */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                🏷️ Product Title
              </label>
              <input
                type="text"
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300"
                placeholder="Product title"
                {...register("title")}
              />
            </div>
            
            {/* Description */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                📝 Description
              </label>
              <textarea
                rows={4}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300 resize-none"
                placeholder="Product description"
                {...register("description")}
              />
            </div>
            
            {/* Price and Category */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                  💰 Price (USD)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300"
                  placeholder="0.00"
                  {...register("price")}
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                  📋 Category
                </label>
                <input
                  type="text"
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300"
                  placeholder="Category"
                  {...register("category")}
                />
              </div>
            </div>
            
            {/* Image URL */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                🖼️ Image URL
              </label>
              <input
                type="url"
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300"
                placeholder="https://example.com/image.jpg"
                {...register("image")}
              />
            </div>
            
            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button 
                type="submit"
                className="flex-1 py-3 bg-gradient-to-r from-blue-600 to-green-600 text-white font-semibold rounded-xl hover:from-blue-500 hover:to-green-500 transform hover:scale-105 transition-all duration-300 shadow-lg flex items-center justify-center gap-2"
              >
                ✨ Update Product
              </button>
              
              <button
                type="button"
                onClick={() => deleteHandler(id)}
                className="flex-1 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white font-semibold rounded-xl hover:from-red-500 hover:to-red-600 transform hover:scale-105 transition-all duration-300 shadow-lg flex items-center justify-center gap-2"
              >
                🗑️ Delete Product
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  ) : (
    <div className="flex flex-col items-center justify-center py-20">
      <div className="text-6xl mb-6 animate-bounce">📦</div>
      <h2 className="text-2xl font-bold text-gray-300 mb-4">Loading Product Details</h2>
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 border-3 border-purple-400 border-t-transparent rounded-full animate-spin"></div>
        <span className="text-purple-300">Please wait while we fetch the product information...</span>
      </div>
    </div>
  );
};

export default ProductDetail;
