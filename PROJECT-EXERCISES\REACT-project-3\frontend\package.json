{"name": "react-project-3", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.8", "axios": "^1.9.0", "json-server": "^1.0.0-beta.3", "nanoid": "^5.1.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-infinite-scroll-component": "^6.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "react-toastify": "^11.0.5", "tailwindcss": "^4.1.8"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}