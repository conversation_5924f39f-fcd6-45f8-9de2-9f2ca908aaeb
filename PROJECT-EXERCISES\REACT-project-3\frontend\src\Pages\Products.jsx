import { lazy, Suspense } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import useInfiniteProducts from "../utils/useInfiniteProducts";

const ProductTemplate = lazy(() => import("../Components/ProductTemplate"));

const Products = () => {
  const {product, hasMore, fetchProducts, loading, error, resetProducts} = useInfiniteProducts();

  // Debug logging
  console.log('Products component render:', {
    productCount: product.length,
    hasMore,
    loading,
    error
  });

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Page Header */}
      <header className="text-center py-6 sm:py-8 lg:py-12">
        <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-3 sm:mb-4 text-gray-900">
          Our Products
        </h1>
        <p className="text-gray-600 text-sm sm:text-base lg:text-lg max-w-2xl mx-auto px-4">
          Discover our amazing collection of products crafted with care and attention to detail
        </p>

        {/* Product Count Badge */}
        {product.length > 0 && (
          <div className="mt-4 inline-flex items-center px-3 py-1 bg-blue-50 border border-blue-200 rounded-full">
            <span className="text-blue-800 text-sm font-medium">
              {product.length} Products Loaded
            </span>
          </div>
        )}

        {/* Debug Info - Remove in production */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-4 text-xs text-gray-500 space-y-1">
            <div>Products: {product.length} | HasMore: {hasMore.toString()} | Loading: {loading.toString()}</div>
            {error && <div className="text-red-500">Error: {error}</div>}
            <button
              onClick={() => {
                console.log('Manual fetch triggered');
                fetchProducts();
              }}
              className="px-2 py-1 bg-gray-200 rounded text-xs"
              disabled={loading}
            >
              Manual Load More
            </button>
          </div>
        )}
      </header>

      {/* Error State */}
      {error && (
        <div className="flex flex-col items-center justify-center py-12 sm:py-20 px-4">
          <div className="text-red-500 text-center max-w-md">
            <div className="text-4xl sm:text-5xl mb-4">⚠️</div>
            <h2 className="text-lg sm:text-xl font-semibold mb-2">Error Loading Products</h2>
            <p className="text-gray-600 text-sm sm:text-base mb-6">{error}</p>
            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={() => {
                  resetProducts();
                  setTimeout(fetchProducts, 100);
                }}
                className="btn-primary"
              >
                Reset & Retry
              </button>
              <button
                onClick={() => window.location.reload()}
                className="btn-secondary"
              >
                Reload Page
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Products Grid */}
      {!error && product.length > 0 && (
        <section className="container-responsive">
          <InfiniteScroll
            dataLength={product.length}
            next={() => {
              console.log('InfiniteScroll triggered next, current state:', {
                productCount: product.length,
                hasMore,
                loading
              });
              if (!loading && hasMore) {
                fetchProducts();
              }
            }}
            loader={
              <div className="flex justify-center items-center py-6 sm:py-8">
                <div className="flex items-center gap-2 text-gray-600">
                  <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-sm sm:text-base">Loading more products...</span>
                </div>
              </div>
            }
            hasMore={hasMore && !loading}
            endMessage={
              <div className="text-center py-6 sm:py-8">
                <div className="inline-block p-4 sm:p-6 bg-green-50 border border-green-200 rounded-xl">
                  <div className="text-green-600 text-2xl sm:text-3xl mb-2">🎉</div>
                  <p className="text-green-800 font-medium text-sm sm:text-base">You've seen all our products!</p>
                  <p className="text-green-600 text-xs sm:text-sm mt-1">Total: {product.length} products</p>
                </div>
              </div>
            }
            scrollThreshold={0.8}
          >
            {/* Responsive Products Grid */}
            <div className="products-grid">
              {product.map((prod) => (
                <Suspense
                  key={`product-${prod.id}`}
                  fallback={
                    <div className="bg-white border border-gray-200 rounded-xl overflow-hidden loading-shimmer">
                      <div className="bg-gray-200 h-48 sm:h-52 md:h-56 lg:h-48 xl:h-52"></div>
                      <div className="p-4 sm:p-5 space-y-3">
                        <div className="bg-gray-200 h-4 rounded w-3/4"></div>
                        <div className="bg-gray-200 h-3 rounded w-full"></div>
                        <div className="bg-gray-200 h-3 rounded w-2/3"></div>
                        <div className="bg-gray-200 h-6 rounded w-1/2 mt-4"></div>
                        <div className="flex gap-2 mt-4">
                          <div className="bg-gray-200 h-10 rounded flex-1"></div>
                          <div className="bg-gray-200 h-10 rounded w-20"></div>
                        </div>
                      </div>
                    </div>
                  }
                >
                  <ProductTemplate product={prod} />
                </Suspense>
              ))}
            </div>
          </InfiniteScroll>
        </section>
      )}
      
      {/* Initial Loading State */}
      {!error && product.length === 0 && (
        <div className="flex flex-col items-center justify-center py-12 sm:py-20">
          <div className="flex flex-col items-center gap-4 text-gray-600">
            <div className="w-8 h-8 sm:w-10 sm:h-10 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            <span className="text-sm sm:text-base">Loading products...</span>
            <p className="text-xs sm:text-sm text-gray-500 text-center max-w-xs">
              Please wait while we fetch the latest products for you
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Products;
