import { lazy, Suspense } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import useInfiniteProducts from "../utils/useInfiniteProducts";

const ProductTemplate = lazy(() => import("../Components/ProductTemplate"));

const Products = () => {
  const {product, hasMore, fetchProducts, loading, error, resetProducts} = useInfiniteProducts();

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="text-center py-8">
        <h1 className="text-3xl font-bold mb-2 text-gray-900">
          Our Products
        </h1>
        <p className="text-gray-600">
          Discover our amazing collection of products
        </p>
      </div>

      {/* Error State */}
      {error && (
        <div className="flex flex-col items-center justify-center py-20">
          <div className="text-red-500 text-center">
            <div className="text-4xl mb-4">⚠️</div>
            <h2 className="text-xl font-semibold mb-2">Error Loading Products</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <div className="flex gap-3">
              <button 
                onClick={() => {
                  resetProducts();
                  setTimeout(fetchProducts, 100);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Reset & Retry
              </button>
              <button 
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Reload Page
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Products Grid */}
      {!error && product.length > 0 && (
        <InfiniteScroll
          dataLength={product.length}
          next={fetchProducts}
          loader={
            <div className="flex justify-center items-center py-8">
              <div className="flex items-center gap-2 text-gray-600">
                <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                <span>Loading more products...</span>
              </div>
            </div>
          }
          hasMore={hasMore && !loading}
          endMessage={
            <div className="text-center py-8">
              <div className="inline-block p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="text-green-600 text-2xl mb-2">🎉</div>
                <p className="text-green-800 font-medium">You've seen all our products!</p>
                <p className="text-green-600 text-sm">Total: {product.length} products</p>
              </div>
            </div>
          }
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {product.map((prod) => (
              <Suspense 
                key={`product-${prod.id}`}
                fallback={
                  <div className="bg-white border border-gray-200 rounded-lg h-[350px] animate-pulse">
                    <div className="bg-gray-200 h-[200px] rounded-t-lg"></div>
                    <div className="p-4 space-y-3">
                      <div className="bg-gray-200 h-4 rounded"></div>
                      <div className="bg-gray-200 h-3 rounded w-3/4"></div>
                      <div className="bg-gray-200 h-6 rounded w-1/2"></div>
                    </div>
                  </div>
                }
              >
                <ProductTemplate product={prod} />
              </Suspense>
            ))}
          </div>
        </InfiniteScroll>
      )}
      
      {/* Initial Loading State */}
      {!error && product.length === 0 && (
        <div className="flex flex-col items-center justify-center py-20">
          <div className="flex items-center gap-3 text-gray-600">
            <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            <span>Loading products...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default Products;
