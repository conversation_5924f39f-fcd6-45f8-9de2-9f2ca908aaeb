import { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import axios from "../api/axiosconfig";
import { loadLazyProducts, clearProducts } from "../store/Reducer/ProductsSlice";

const useInfiniteProducts = () => {
  const dispatch = useDispatch();
  const product = useSelector((state) => state.products.data);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchProducts = useCallback(async () => {
    if (loading) return; // Prevent multiple simultaneous calls

    try {
      setLoading(true);
      setError(null);

      // Use JSON Server v1.0.0-beta.3 pagination format: _start and _limit
      const start = product.length;
      const limit = 6;
      const { data } = await axios.get(
        `/products?_start=${start}&_limit=${limit}`
      );

      console.log(`Fetching products starting from ${start}, limit ${limit}, received data:`, data);

      // Validate that data is an array
      let productsArray = [];
      if (Array.isArray(data)) {
        productsArray = data;
      } else if (data && Array.isArray(data.products)) {
        // If the response has a products property
        productsArray = data.products;
      } else if (data && typeof data === 'object') {
        // If it's a single product object, wrap it in an array
        productsArray = [data];
      } else {
        console.warn('Unexpected data format:', data);
        productsArray = [];
      }

      console.log(`Processed ${productsArray.length} products starting from index ${start}`);

      if (productsArray.length === 0) {
        console.log('No more products available, setting hasMore to false');
        setHasMore(false);
      } else {
        dispatch(loadLazyProducts(productsArray));
        // If we received less than the limit, we've reached the end
        if (productsArray.length < limit) {
          console.log(`Received ${productsArray.length} products (less than limit ${limit}), setting hasMore to false`);
          setHasMore(false);
        }
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      setError(error.message || 'Failed to fetch products');
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  }, [dispatch, product.length]);
  
  const resetProducts = useCallback(() => {
    dispatch(clearProducts());
    setHasMore(true);
    setLoading(false);
    setError(null);
  }, [dispatch]);
  
  useEffect(() => {
    if (product.length === 0 && !loading && !error) {
      console.log('Initial load: fetching first batch of products');
      fetchProducts();
    }
  }, []); // Only run on mount
  
  return {product, hasMore, fetchProducts, loading, error, resetProducts};
};

export default useInfiniteProducts;
