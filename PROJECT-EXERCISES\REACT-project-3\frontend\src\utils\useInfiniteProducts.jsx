import { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import axios from "../api/axiosconfig";
import { loadLazyProducts, clearProducts } from "../store/Reducer/ProductsSlice";

const useInfiniteProducts = () => {
  const dispatch = useDispatch();
  const product = useSelector((state) => state.products.data);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const fetchProducts = useCallback(async () => {
    if (loading) return; // Prevent multiple simultaneous calls
    
    try {
      setLoading(true);
      setError(null);
      
      // Use JSON Server pagination format: _page and _limit
      const page = Math.floor(product.length / 6) + 1;
      const { data } = await axios.get(
        `/products?_page=${page}&_limit=6`
      );
      
      console.log(`Fetching page ${page}, received data:`, data);
      
      // Validate that data is an array
      let productsArray = [];
      if (Array.isArray(data)) {
        productsArray = data;
      } else if (data && Array.isArray(data.products)) {
        // If the response has a products property
        productsArray = data.products;
      } else if (data && typeof data === 'object') {
        // If it's a single product object, wrap it in an array
        productsArray = [data];
      } else {
        console.warn('Unexpected data format:', data);
        productsArray = [];
      }
      
      console.log(`Processed ${productsArray.length} products for page ${page}`);
      
      if (productsArray.length === 0) {
        setHasMore(false);
      } else {
        dispatch(loadLazyProducts(productsArray));
        // If we received less than the limit, we've reached the end
        if (productsArray.length < 6) {
          setHasMore(false);
        }
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      setError(error.message);
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  }, [dispatch, product.length, loading]);
  
  const resetProducts = useCallback(() => {
    dispatch(clearProducts());
    setHasMore(true);
    setLoading(false);
    setError(null);
  }, [dispatch]);
  
  useEffect(() => {
    if (product.length === 0) {
      fetchProducts();
    }
  }, []);
  
  return {product, hasMore, fetchProducts, loading, error, resetProducts};
};

export default useInfiniteProducts;
