import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { useEffect } from "react";
import { asyncCurrentUser, asyncDeleteUsers, asyncLogoutUser, asyncUpdateUser } from "../../store/Actions/UserAction";

const Profile = () => {
  const { id } = useParams();
  const user = useSelector((state) => state.users.data); 
  const dispatch = useDispatch();
  const navigate = useNavigate();

  useEffect(() => {
    dispatch(asyncCurrentUser());
  }, [dispatch]);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: user?.name,
      email: user?.email,
      username: user?.username,
      password: user?.password
    },
  });

  const updateUserProfile = (formData) => {
    console.log("Form data:", formData);
    console.log("User ID:", user?.id);

    if (!user?.id) {
      console.error("User ID is missing!");
      return;
    }

    dispatch(asyncUpdateUser(user.id, formData)); 
  };

  const deleteUserProfile = () => {
    dispatch(asyncDeleteUsers(user.id));
    navigate("/login");
  };
  
  const logoutUserProfile = () => {
    dispatch(asyncLogoutUser());
  };

  return user ? (
    <div className="flex items-center justify-center py-12">
      <div className="w-full max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-3xl font-bold text-white">
              {user.name?.charAt(0)?.toUpperCase()}
            </span>
          </div>
          <h1 className="text-4xl font-bold mb-3 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            👤 User Profile
          </h1>
          <p className="text-gray-300">Manage your account settings and preferences</p>
        </div>
        
        {/* Profile Card */}
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/20 rounded-2xl p-8 shadow-2xl">
          <form onSubmit={handleSubmit(updateUserProfile)} className="space-y-6">
            {/* Name Field */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                👤 Full Name
              </label>
              <input
                type="text"
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                placeholder="John Doe"
                {...register("name")}
              />
            </div>
            
            {/* Username Field */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                🏷️ Username
              </label>
              <input
                type="text"
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                placeholder="john1234"
                {...register("username")}
              />
            </div>
            
            {/* Email Field */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                📬 Email Address
              </label>
              <input
                type="email"
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                placeholder="<EMAIL>"
                {...register("email")}
              />
            </div>
            
            {/* Password Field */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                🔒 Password
              </label>
              <input
                type="password"
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                placeholder="Update password"
                {...register("password")}
              />
            </div>
            
            {/* Update Button */}
            <button 
              type="submit"
              className="w-full py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-500 hover:to-purple-500 transform hover:scale-105 transition-all duration-300 shadow-2xl flex items-center justify-center gap-2"
            >
              ✨ Update Profile
            </button>
          </form>
          
          {/* Danger Zone */}
          <div className="mt-8 pt-6 border-t border-red-400/30">
            <h3 className="text-lg font-bold text-red-400 mb-4 flex items-center gap-2">
              ⚠️ Danger Zone
            </h3>
            <div className="grid sm:grid-cols-2 gap-4">
              <button 
                type="button"
                onClick={logoutUserProfile}
                className="py-3 bg-gradient-to-r from-yellow-600 to-orange-600 text-white font-semibold rounded-xl hover:from-yellow-500 hover:to-orange-500 transform hover:scale-105 transition-all duration-300 shadow-lg flex items-center justify-center gap-2"
              >
                🚪 Logout
              </button>
              
              <button 
                type="button"
                onClick={deleteUserProfile}
                className="py-3 bg-gradient-to-r from-red-600 to-red-700 text-white font-semibold rounded-xl hover:from-red-500 hover:to-red-600 transform hover:scale-105 transition-all duration-300 shadow-lg flex items-center justify-center gap-2"
              >
                🗑️ Delete Account
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  ) : (
    <div className="flex flex-col items-center justify-center py-20">
      <div className="text-6xl mb-6 animate-bounce">👤</div>
      <h2 className="text-2xl font-bold text-gray-300 mb-4">Loading Profile</h2>
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 border-3 border-purple-400 border-t-transparent rounded-full animate-spin"></div>
        <span className="text-purple-300">Please wait while we load your profile...</span>
      </div>
    </div>
  )
}

export default Profile