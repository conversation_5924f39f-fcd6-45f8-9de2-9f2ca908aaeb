@import "tailwindcss";

/* Line clamp utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus styles */
button:focus,
input:focus,
textarea:focus {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6;
}

/* Responsive utilities and improvements */
/* Ensure minimum touch target size for mobile */
@media (max-width: 768px) {
  button,
  a[role="button"],
  input[type="button"],
  input[type="submit"] {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Product card hover animations */
.product-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

.product-image {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Improved button styles for better mobile interaction */
.btn-primary {
  @apply px-4 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 active:bg-blue-800 transition-all duration-200 transform active:scale-95;
}

.btn-secondary {
  @apply px-4 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 active:bg-gray-100 transition-all duration-200 transform active:scale-95;
}

/* Grid system improvements */
.products-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 480px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
}

@media (min-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1280px) {
  .products-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1536px) {
  .products-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 2rem;
  }
}

/* Responsive text scaling */
.responsive-text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

@media (min-width: 768px) {
  .responsive-text-sm {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.responsive-text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

@media (min-width: 768px) {
  .responsive-text-base {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

/* Container improvements */
.container-responsive {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px) {
  .container-responsive {
    max-width: 1280px;
  }
}

/* Prevent horizontal scroll on mobile */
html, body {
  overflow-x: hidden;
}

/* Improved loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
