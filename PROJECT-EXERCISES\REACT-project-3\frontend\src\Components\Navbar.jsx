import React, { useState } from 'react'
import { useSelector, useDispatch } from 'react-redux';
import { NavLink } from 'react-router-dom'
// import { asyncLogoutUser } from '../store/Actions/UserAction';

const Navbar = () => {
  const user = useSelector((state) => state.users.data);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className='bg-white border-b border-gray-200 shadow-sm sticky top-0 z-50'>
      <div className='container-responsive py-3 sm:py-4'>
        <div className='flex items-center justify-between h-12 sm:h-14'>
          {/* Logo/Brand */}
          <NavLink to="/" className='flex items-center gap-2 sm:gap-3 hover:opacity-80 transition-opacity'>
            <div className='w-8 h-8 sm:w-9 sm:h-9 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-sm'>
              <span className='text-white font-bold text-sm sm:text-base'>E</span>
            </div>
            <span className='text-lg sm:text-xl lg:text-2xl font-bold text-gray-900'>
              E-Store
            </span>
          </NavLink>
          
          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className='md:hidden p-3 rounded-xl text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 active:scale-95'
            aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
            aria-expanded={isMenuOpen}
          >
            <svg className='w-6 h-6' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
              {isMenuOpen ? (
                <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M6 18L18 6M6 6l12 12' />
              ) : (
                <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M4 6h16M4 12h16M4 18h16' />
              )}
            </svg>
          </button>
          
          {/* Desktop Navigation */}
          <div className='hidden md:flex items-center gap-1 lg:gap-2'>
          <NavLink
            to={"/"}
            className={({ isActive }) =>
              `px-4 py-2.5 rounded-lg text-sm lg:text-base font-medium transition-all duration-200 ${
                isActive
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              }`
            }
          >
            Home
          </NavLink>
          
          <NavLink
            to={"/products"}
            className={({ isActive }) =>
              `px-4 py-2.5 rounded-lg text-sm lg:text-base font-medium transition-all duration-200 ${
                isActive
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              }`
            }
          >
            Products
          </NavLink>
          
          {user ? (
            <>
              <NavLink 
                to={"/admin/create-product"} 
                className={({ isActive }) => 
                  `px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }`
                }
              >
                Create
              </NavLink>
              
              <NavLink 
                to={"/admin/user-profile"} 
                className={({ isActive }) => 
                  `px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }`
                }
              >
                Settings
              </NavLink>
              
              <NavLink 
                to={"/cart"} 
                className={({ isActive }) => 
                  `px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }`
                }
              >
                Cart
              </NavLink>
              
              <div className="flex items-center gap-2 ml-4 p-2 bg-gray-100 rounded-lg">
                <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-xs font-bold text-white">
                    {user.name?.charAt(0)?.toUpperCase()}
                  </span>
                </div>
                <span className="text-gray-700 text-sm font-medium">Hi, {user.name}</span>
              </div>
            </>
          ) : (
            <>
              <NavLink 
                to={"/login"} 
                className={({ isActive }) => 
                  `px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-blue-600 hover:bg-blue-700 text-white'
                  }`
                }
              >
                Login
              </NavLink>
              
              <NavLink 
                to={"/register"} 
                className={({ isActive }) => 
                  `px-4 py-2 rounded-md text-sm font-medium border border-blue-600 transition-colors ${
                    isActive 
                      ? 'bg-blue-600 text-white' 
                      : 'text-blue-600 hover:bg-blue-50'
                  }`
                }
              >
                Register
              </NavLink>
            </>
          )}
          </div>
        </div>
        
        {/* Mobile Navigation Menu */}
        <div className={`md:hidden transition-all duration-300 ease-in-out ${
          isMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0 overflow-hidden'
        }`}>
          <div className='px-3 pt-4 pb-4 space-y-2 bg-gray-50 rounded-xl mt-4 border border-gray-200 shadow-sm'>
            <NavLink
              to="/"
              onClick={() => setIsMenuOpen(false)}
              className={({ isActive }) =>
                `block px-4 py-3 rounded-xl text-base font-medium transition-all duration-200 ${
                  isActive
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-700 hover:bg-white hover:text-gray-900 hover:shadow-sm'
                }`
              }
            >
              🏠 Home
            </NavLink>

            <NavLink
              to="/products"
              onClick={() => setIsMenuOpen(false)}
              className={({ isActive }) =>
                `block px-4 py-3 rounded-xl text-base font-medium transition-all duration-200 ${
                  isActive
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-700 hover:bg-white hover:text-gray-900 hover:shadow-sm'
                }`
              }
            >
              🛍️ Products
            </NavLink>
            
            {user ? (
              <>
                <NavLink 
                  to="/admin/create-product" 
                  onClick={() => setIsMenuOpen(false)}
                  className={({ isActive }) => 
                    `block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                      isActive 
                        ? 'bg-blue-600 text-white' 
                        : 'text-gray-700 hover:bg-gray-100'
                    }`
                  }
                >
                  ➕ Create Product
                </NavLink>
                
                <NavLink 
                  to="/admin/user-profile" 
                  onClick={() => setIsMenuOpen(false)}
                  className={({ isActive }) => 
                    `block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                      isActive 
                        ? 'bg-blue-600 text-white' 
                        : 'text-gray-700 hover:bg-gray-100'
                    }`
                  }
                >
                  ⚙️ Settings
                </NavLink>
                
                <NavLink 
                  to="/cart" 
                  onClick={() => setIsMenuOpen(false)}
                  className={({ isActive }) => 
                    `block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                      isActive 
                        ? 'bg-blue-600 text-white' 
                        : 'text-gray-700 hover:bg-gray-100'
                    }`
                  }
                >
                  🛒 Cart
                </NavLink>
                
                <div className="px-3 py-2 flex items-center gap-2">
                  <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-white">
                      {user.name?.charAt(0)?.toUpperCase()}
                    </span>
                  </div>
                  <span className="text-gray-700 text-sm font-medium">Hi, {user.name}</span>
                </div>
              </>
            ) : (
              <>
                <NavLink 
                  to="/login" 
                  onClick={() => setIsMenuOpen(false)}
                  className="block w-full text-center px-4 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors"
                >
                  🔐 Login
                </NavLink>
                
                <NavLink 
                  to="/register" 
                  onClick={() => setIsMenuOpen(false)}
                  className="block w-full text-center px-4 py-3 border border-blue-600 text-blue-600 font-medium rounded-md hover:bg-blue-50 transition-colors"
                >
                  ✨ Register
                </NavLink>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  )
}

export default Navbar
