import React, { useState } from 'react'
import { useSelector, useDispatch } from 'react-redux';
import { NavLink } from 'react-router-dom'
// import { asyncLogoutUser } from '../store/Actions/UserAction';

const Navbar = () => {
  const user = useSelector((state) => state.users.data);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className='bg-white border-b border-gray-200 shadow-sm mb-4 sm:mb-6 md:mb-8'>
      <div className='px-3 sm:px-4 md:px-6 lg:px-8 py-3 sm:py-4'>
        <div className='flex items-center justify-between'>
          {/* Logo/Brand */}
          <div className='flex items-center gap-2 sm:gap-3'>
            <div className='w-7 h-7 sm:w-8 sm:h-8 bg-blue-600 rounded-lg flex items-center justify-center'>
              <span className='text-white font-bold text-xs sm:text-sm'>E</span>
            </div>
            <span className='text-lg sm:text-xl font-bold text-gray-900'>
              E-Store
            </span>
          </div>
          
          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className='md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500'
            aria-label='Toggle menu'
          >
            <svg className='w-6 h-6' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
              {isMenuOpen ? (
                <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M6 18L18 6M6 6l12 12' />
              ) : (
                <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M4 6h16M4 12h16M4 18h16' />
              )}
            </svg>
          </button>
          
          {/* Desktop Navigation */}
          <div className='hidden md:flex items-center gap-1 lg:gap-2'>
          <NavLink 
            to={"/"} 
            className={({ isActive }) => 
              `px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isActive 
                  ? 'bg-blue-600 text-white' 
                  : 'text-gray-700 hover:bg-gray-100'
              }`
            }
          >
            Home
          </NavLink>
          
          <NavLink 
            to={"/products"} 
            className={({ isActive }) => 
              `px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isActive 
                  ? 'bg-blue-600 text-white' 
                  : 'text-gray-700 hover:bg-gray-100'
              }`
            }
          >
            Products
          </NavLink>
          
          {user ? (
            <>
              <NavLink 
                to={"/admin/create-product"} 
                className={({ isActive }) => 
                  `px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }`
                }
              >
                Create
              </NavLink>
              
              <NavLink 
                to={"/admin/user-profile"} 
                className={({ isActive }) => 
                  `px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }`
                }
              >
                Settings
              </NavLink>
              
              <NavLink 
                to={"/cart"} 
                className={({ isActive }) => 
                  `px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }`
                }
              >
                Cart
              </NavLink>
              
              <div className="flex items-center gap-2 ml-4 p-2 bg-gray-100 rounded-lg">
                <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-xs font-bold text-white">
                    {user.name?.charAt(0)?.toUpperCase()}
                  </span>
                </div>
                <span className="text-gray-700 text-sm font-medium">Hi, {user.name}</span>
              </div>
            </>
          ) : (
            <>
              <NavLink 
                to={"/login"} 
                className={({ isActive }) => 
                  `px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-blue-600 hover:bg-blue-700 text-white'
                  }`
                }
              >
                Login
              </NavLink>
              
              <NavLink 
                to={"/register"} 
                className={({ isActive }) => 
                  `px-4 py-2 rounded-md text-sm font-medium border border-blue-600 transition-colors ${
                    isActive 
                      ? 'bg-blue-600 text-white' 
                      : 'text-blue-600 hover:bg-blue-50'
                  }`
                }
              >
                Register
              </NavLink>
            </>
          )}
          </div>
        </div>
        
        {/* Mobile Navigation Menu */}
        <div className={`md:hidden transition-all duration-300 ease-in-out ${
          isMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0 overflow-hidden'
        }`}>
          <div className='px-2 pt-2 pb-3 space-y-1 bg-gray-50 rounded-lg mt-4'>
            <NavLink 
              to="/" 
              onClick={() => setIsMenuOpen(false)}
              className={({ isActive }) => 
                `block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                  isActive 
                    ? 'bg-blue-600 text-white' 
                    : 'text-gray-700 hover:bg-gray-100'
                }`
              }
            >
              🏠 Home
            </NavLink>
            
            <NavLink 
              to="/products" 
              onClick={() => setIsMenuOpen(false)}
              className={({ isActive }) => 
                `block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                  isActive 
                    ? 'bg-blue-600 text-white' 
                    : 'text-gray-700 hover:bg-gray-100'
                }`
              }
            >
              🛍️ Products
            </NavLink>
            
            {user ? (
              <>
                <NavLink 
                  to="/admin/create-product" 
                  onClick={() => setIsMenuOpen(false)}
                  className={({ isActive }) => 
                    `block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                      isActive 
                        ? 'bg-blue-600 text-white' 
                        : 'text-gray-700 hover:bg-gray-100'
                    }`
                  }
                >
                  ➕ Create Product
                </NavLink>
                
                <NavLink 
                  to="/admin/user-profile" 
                  onClick={() => setIsMenuOpen(false)}
                  className={({ isActive }) => 
                    `block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                      isActive 
                        ? 'bg-blue-600 text-white' 
                        : 'text-gray-700 hover:bg-gray-100'
                    }`
                  }
                >
                  ⚙️ Settings
                </NavLink>
                
                <NavLink 
                  to="/cart" 
                  onClick={() => setIsMenuOpen(false)}
                  className={({ isActive }) => 
                    `block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                      isActive 
                        ? 'bg-blue-600 text-white' 
                        : 'text-gray-700 hover:bg-gray-100'
                    }`
                  }
                >
                  🛒 Cart
                </NavLink>
                
                <div className="px-3 py-2 flex items-center gap-2">
                  <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-white">
                      {user.name?.charAt(0)?.toUpperCase()}
                    </span>
                  </div>
                  <span className="text-gray-700 text-sm font-medium">Hi, {user.name}</span>
                </div>
              </>
            ) : (
              <>
                <NavLink 
                  to="/login" 
                  onClick={() => setIsMenuOpen(false)}
                  className="block w-full text-center px-4 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors"
                >
                  🔐 Login
                </NavLink>
                
                <NavLink 
                  to="/register" 
                  onClick={() => setIsMenuOpen(false)}
                  className="block w-full text-center px-4 py-3 border border-blue-600 text-blue-600 font-medium rounded-md hover:bg-blue-50 transition-colors"
                >
                  ✨ Register
                </NavLink>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  )
}

export default Navbar
