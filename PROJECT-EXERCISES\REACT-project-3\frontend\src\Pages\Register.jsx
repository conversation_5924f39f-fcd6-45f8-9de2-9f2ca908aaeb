import { useForm } from "react-hook-form";
import { nanoid } from "nanoid";
import { Link, Navigate, NavLink, useNavigate } from "react-router-dom";
import { asyncRegisterUser } from "../store/Actions/UserAction";
import { useDispatch } from "react-redux";
const Register = () => {
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();
  const dispatch = useDispatch();

  const registerHandler = (user) => {
    user.id = nanoid();
    user.isAdmin = false;
    user.cart = []
    dispatch(asyncRegisterUser(user));
    navigate("/login");
  };

  return (
    <div className="flex items-center justify-center py-12">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-3 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            🎆 Join E-Store
          </h1>
          <p className="text-gray-300">Create your account and start shopping today</p>
        </div>
        
        {/* Form Card */}
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/20 rounded-2xl p-8 shadow-2xl">
          <form onSubmit={handleSubmit(registerHandler)} className="space-y-6">
            {/* Name Field */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                👤 Full Name
              </label>
              <input
                type="text"
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                placeholder="John Doe"
                {...register("name")}
              />
            </div>
            
            {/* Username Field */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                🏷️ Username
              </label>
              <input
                type="text"
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                placeholder="john1234"
                {...register("username")}
              />
            </div>
            
            {/* Email Field */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                📬 Email Address
              </label>
              <input
                type="email"
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                placeholder="<EMAIL>"
                {...register("email")}
              />
            </div>
            
            {/* Password Field */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                🔒 Password
              </label>
              <input
                type="password"
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                placeholder="Create a strong password"
                {...register("password")}
              />
            </div>
            
            {/* Submit Button */}
            <button 
              type="submit" 
              className="w-full py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-xl hover:from-purple-500 hover:to-pink-500 transform hover:scale-105 transition-all duration-300 shadow-2xl"
            >
              ✨ Create Account
            </button>
          </form>
          
          {/* Divider */}
          <div className="my-6 flex items-center">
            <div className="flex-1 border-t border-gray-600"></div>
            <span className="px-4 text-gray-400 text-sm">or</span>
            <div className="flex-1 border-t border-gray-600"></div>
          </div>
          
          {/* Login Link */}
          <div className="text-center">
            <p className="text-gray-300 mb-3">Already have an account?</p>
            <Link 
              to="/login" 
              className="inline-block px-6 py-3 border-2 border-blue-400 text-blue-300 font-semibold rounded-xl hover:bg-blue-500 hover:text-white transition-all duration-300 transform hover:scale-105"
            >
              🔐 Sign In Instead
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
