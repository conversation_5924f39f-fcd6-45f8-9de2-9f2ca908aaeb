import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  data: [],
  loading: false,
  error: null,
};

const productsSlice = createSlice({
  name: "products",
  initialState,
  reducers: {
    loadProducts: (state, action) => {
      state.data = action.payload;
      state.loading = false;
      state.error = null;
    },
    loadLazyProducts: (state, action) => {
      // Ensure payload is an array
      let payload = action.payload;
      if (!Array.isArray(payload)) {
        console.warn('loadLazyProducts expected array, received:', payload);
        payload = Array.isArray(payload?.products) ? payload.products : [];
      }
      
      // Filter out duplicates by checking existing IDs
      const existingIds = new Set(state.data.map(product => product.id));
      const newProducts = payload.filter(product => product && product.id && !existingIds.has(product.id));
      
      state.data = [...state.data, ...newProducts];
      state.loading = false;
      state.error = null;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
      state.loading = false;
    },
    clearProducts: (state) => {
      state.data = [];
      state.loading = false;
      state.error = null;
    },
  },
});

export const { loadProducts, loadLazyProducts, setLoading, setError, clearProducts } = productsSlice.actions;
export default productsSlice.reducer;
