import { useForm } from "react-hook-form";
import { Link, useNavigate } from 'react-router-dom'
import { asyncLoginUser } from "../store/Actions/UserAction";
import { useDispatch } from "react-redux";

const Login = () => {
  const { register, handleSubmit, formState: { errors } } = useForm();
  const naviagte = useNavigate();
  const dispatch = useDispatch();
  const loginHandler = (user) => {
    dispatch(asyncLoginUser(user));
    naviagte('/products')
  };

 return (
    <div className="flex items-center justify-center py-12">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-3 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            🔐 Welcome Back
          </h1>
          <p className="text-gray-300">Sign in to your account to continue shopping</p>
        </div>
        
        {/* Form Card */}
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/20 rounded-2xl p-8 shadow-2xl">
          <form onSubmit={handleSubmit(loginHandler)} className="space-y-6">
            {/* Email Field */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                📬 Email Address
              </label>
              <input 
                type="email" 
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300" 
                placeholder="<EMAIL>" 
                {...register("email")} 
              />
            </div>
            
            {/* Password Field */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                🔒 Password
              </label>
              <input 
                type="password" 
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300" 
                placeholder="Enter your password" 
                {...register("password")} 
              />
            </div>
            
            {/* Submit Button */}
            <button 
              type="submit" 
              className="w-full py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-500 hover:to-purple-500 transform hover:scale-105 transition-all duration-300 shadow-2xl"
            >
              ✨ Sign In
            </button>
          </form>
          
          {/* Divider */}
          <div className="my-6 flex items-center">
            <div className="flex-1 border-t border-gray-600"></div>
            <span className="px-4 text-gray-400 text-sm">or</span>
            <div className="flex-1 border-t border-gray-600"></div>
          </div>
          
          {/* Register Link */}
          <div className="text-center">
            <p className="text-gray-300 mb-3">Don't have an account?</p>
            <Link 
              to="/register" 
              className="inline-block px-6 py-3 border-2 border-purple-400 text-purple-300 font-semibold rounded-xl hover:bg-purple-500 hover:text-white transition-all duration-300 transform hover:scale-105"
            >
              🎆 Create Account
            </Link>
          </div>
        </div>
      </div>
    </div>
  );

}

export default Login
