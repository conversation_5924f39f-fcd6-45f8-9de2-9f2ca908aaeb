{"hash": "a232734b", "configHash": "d0d701a5", "lockfileHash": "04da5435", "browserHash": "36feefc2", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "a7809a71", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "c1f5d9e4", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "8c649993", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "6cb254b6", "needsInterop": true}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "d1399b87", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "4d01e04c", "needsInterop": false}, "nanoid": {"src": "../../nanoid/index.browser.js", "file": "nanoid.js", "fileHash": "020e50e3", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "5b9b88eb", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "bbb35382", "needsInterop": false}, "react-infinite-scroll-component": {"src": "../../react-infinite-scroll-component/dist/index.es.js", "file": "react-infinite-scroll-component.js", "fileHash": "3fd5aa01", "needsInterop": false}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "a621ff9f", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "f1973a87", "needsInterop": false}, "react-toastify": {"src": "../../react-toastify/dist/index.mjs", "file": "react-toastify.js", "fileHash": "87c1aeb0", "needsInterop": false}}, "chunks": {"chunk-A7ECLLTJ": {"file": "chunk-A7ECLLTJ.js"}, "chunk-HSUUC2QV": {"file": "chunk-HSUUC2QV.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}