import { useDispatch, useSelector } from "react-redux";
import { asyncUpdateUser } from "../store/Actions/UserAction";

const Cart = () => {
  const dispatch = useDispatch();
  const user = useSelector((state) => state.users.data);
  const products = useSelector((state) => state.products.data);

  const increaseQuantity = (index) => {
    const copyUser = { ...user, cart: [...user.cart] };

    copyUser.cart[index] = {
      productid: copyUser.cart[index].productid,
      quantity: copyUser.cart[index].quantity + 1,
    };

    dispatch(asyncUpdateUser(copyUser.id, copyUser));
  };
  
  const decreaseQuantity = (index) => {
    const copyUser = { ...user, cart: [...user.cart] };

    if(user.cart[index].quantity > 1) {
      copyUser.cart[index] = {
        productid: copyUser.cart[index].productid,
        quantity: copyUser.cart[index].quantity - 1,
      };
    } else {
      copyUser.cart = copyUser.cart.filter((_, i) => i !== index);
    }

    dispatch(asyncUpdateUser(copyUser.id, copyUser));
  };

  const removeItem = (index) => {
    const copyUser = { ...user, cart: [...user.cart] };
    copyUser.cart = copyUser.cart.filter((_, i) => i !== index);
    dispatch(asyncUpdateUser(copyUser.id, copyUser));
  };

  if (!user || !user.cart || user.cart.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-20">
        <div className="text-6xl mb-4">🛒</div>
        <h2 className="text-2xl font-bold text-gray-900 mb-3">Your Cart is Empty</h2>
        <p className="text-gray-600 mb-6 text-center max-w-md">
          Looks like you haven't added any items to your cart yet. Start shopping to see your items here!
        </p>
        <a 
          href="/products" 
          className="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
        >
          Start Shopping
        </a>
      </div>
    );
  }

  // Calculate total
  const calculateTotal = () => {
    return user.cart.reduce((total, item) => {
      const product = products.find(p => p.id === item.productid);
      return total + (product ? product.price * item.quantity : 0);
    }, 0).toFixed(2);
  };

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
          🛒 Your Shopping Cart
        </h1>
        <p className="text-gray-300">Review your items and proceed to checkout</p>
      </div>

      {/* Cart Items */}
      <div className="space-y-4">
        {user.cart.map((item, index) => {
          const product = products.find(p => p.id === item.productid);
          if (!product) return null;
          
          return (
            <div key={index} className="bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm border border-white/20 rounded-2xl p-6 hover:border-purple-400/50 transition-all duration-300">
              <div className="flex flex-col lg:flex-row items-center gap-6">
                {/* Product Image */}
                <div className="flex-shrink-0">
                  <img 
                    src={product.image} 
                    alt={product.title} 
                    className="w-24 h-24 lg:w-32 lg:h-32 object-cover rounded-xl shadow-lg" 
                  />
                </div>
                
                {/* Product Details */}
                <div className="flex-1 text-center lg:text-left">
                  <h3 className="text-xl font-bold text-white mb-2 line-clamp-2">{product.title}</h3>
                  <p className="text-gray-300 text-sm mb-2 line-clamp-1">{product.description.slice(0, 80)}...</p>
                  <div className="flex items-center justify-center lg:justify-start gap-4">
                    <span className="text-lg font-semibold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
              ₹{(product.price * 80).toFixed(0)}
                    </span>
                    <span className="px-2 py-1 bg-purple-500/20 text-purple-300 text-xs font-medium rounded-full">
                      {product.category}
                    </span>
                  </div>
                </div>
                
                {/* Quantity Controls */}
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-3 bg-gray-800/50 rounded-xl p-2">
                    <button 
                      onClick={() => decreaseQuantity(index)}
                      className="w-8 h-8 bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-400 hover:to-pink-400 text-white rounded-lg flex items-center justify-center font-bold transition-all duration-300 hover:scale-110"
                    >
                      -
                    </button>
                    <span className="mx-3 font-bold text-white min-w-[2rem] text-center">{item.quantity}</span>
                    <button 
                      onClick={() => increaseQuantity(index)}
                      className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-400 hover:to-blue-400 text-white rounded-lg flex items-center justify-center font-bold transition-all duration-300 hover:scale-110"
                    >
                      +
                    </button>
                  </div>
                  
                  <button 
                    onClick={() => removeItem(index)}
                    className="px-4 py-2 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white font-semibold rounded-lg transition-all duration-300 hover:scale-105 flex items-center gap-2"
                  >
                    🗑️ Remove
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      {/* Cart Summary */}
      <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm border border-blue-400/30 rounded-2xl p-8">
        <div className="flex flex-col lg:flex-row justify-between items-center gap-6">
          <div className="text-center lg:text-left">
            <h3 className="text-2xl font-bold text-white mb-2">Order Summary</h3>
            <p className="text-gray-300">Total items: {user.cart.reduce((total, item) => total + item.quantity, 0)}</p>
          </div>
          
          <div className="text-center lg:text-right">
            <p className="text-lg text-gray-300 mb-2">Total Amount:</p>
            <p className="text-4xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
              ${calculateTotal()}
            </p>
          </div>
        </div>
        
        <div className="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
          <a 
            href="/products" 
            className="px-6 py-3 border-2 border-purple-400 text-purple-300 font-semibold rounded-xl hover:bg-purple-500 hover:text-white transition-all duration-300 text-center"
          >
            ← Continue Shopping
          </a>
          <button className="px-8 py-3 bg-gradient-to-r from-green-600 to-blue-600 text-white font-semibold rounded-xl hover:from-green-500 hover:to-blue-500 transform hover:scale-105 transition-all duration-300 shadow-2xl">
            💳 Proceed to Checkout
          </button>
        </div>
      </div>
    </div>
  );
};

export default Cart;
