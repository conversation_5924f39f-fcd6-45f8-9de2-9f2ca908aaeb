import { useForm } from "react-hook-form";
import { nanoid } from "nanoid";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { asyncCreateProducts } from "../../store/Actions/productAction";

const CreateProduct = () => {
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();
  const dispatch = useDispatch();

  const addProductHandler = (product) => {
    dispatch(asyncCreateProducts(product));
  };

  return (
    <div className="flex items-center justify-center py-12">
      <div className="w-full max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-3 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            ➕ Create New Product
          </h1>
          <p className="text-gray-300">Add a new product to your store inventory</p>
        </div>
        
        {/* Form Card */}
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/20 rounded-2xl p-8 shadow-2xl">
          <form onSubmit={handleSubmit(addProductHandler)} className="space-y-6">
            {/* Product Title */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                🏷️ Product Title
              </label>
              <input
                type="text"
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                placeholder="e.g., Asus TUF Gaming A15"
                {...register("title")}
              />
            </div>
            
            {/* Description */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                📝 Description
              </label>
              <textarea
                rows={4}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 resize-none"
                placeholder="Detailed description of your product..."
                {...register("description")}
              />
            </div>
            
            {/* Price and Category Row */}
            <div className="grid md:grid-cols-2 gap-6">
              {/* Price */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                  💰 Price (USD)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                  placeholder="0.00"
                  {...register("price")}
                />
              </div>
              
              {/* Category */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                  📋 Category
                </label>
                <input
                  type="text"
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                  placeholder="e.g., Electronics, Gaming, Laptops"
                  {...register("category")}
                />
              </div>
            </div>
            
            {/* Image URL */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                🖼️ Product Image URL
              </label>
              <input
                type="url"
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                placeholder="https://example.com/image.jpg"
                {...register("image")}
              />
            </div>
            
            {/* Submit Button */}
            <button 
              type="submit"
              className="w-full py-4 bg-gradient-to-r from-green-600 to-blue-600 text-white font-semibold rounded-xl hover:from-green-500 hover:to-blue-500 transform hover:scale-105 transition-all duration-300 shadow-2xl flex items-center justify-center gap-2"
            >
              <span>✨</span>
              Create Product
            </button>
          </form>
          
          {/* Back to Products Link */}
          <div className="mt-6 text-center">
            <a 
              href="/products" 
              className="inline-block px-6 py-3 border-2 border-gray-400 text-gray-300 font-semibold rounded-xl hover:bg-gray-500 hover:text-white transition-all duration-300 transform hover:scale-105"
            >
              ← Back to Products
            </a>
          </div>
        </div>
      </div>
    </div>
  )
};

export default CreateProduct;
