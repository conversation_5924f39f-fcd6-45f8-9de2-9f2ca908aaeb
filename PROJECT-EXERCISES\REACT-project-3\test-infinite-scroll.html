<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Infinite Scroll Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .product { border: 1px solid #ccc; margin: 10px 0; padding: 10px; }
        .loading { text-align: center; padding: 20px; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Infinite Scroll API Test</h1>
    <div class="debug">
        <h3>Debug Info</h3>
        <div id="debug-info"></div>
        <button onclick="loadMore()">Manual Load More</button>
        <button onclick="reset()">Reset</button>
    </div>
    
    <div id="products-container"></div>
    <div id="loading" class="loading" style="display: none;">Loading...</div>

    <script>
        let products = [];
        let loading = false;
        let hasMore = true;

        function updateDebugInfo() {
            document.getElementById('debug-info').innerHTML = `
                Products loaded: ${products.length}<br>
                Loading: ${loading}<br>
                Has more: ${hasMore}
            `;
        }

        async function loadMore() {
            if (loading || !hasMore) return;
            
            loading = true;
            document.getElementById('loading').style.display = 'block';
            updateDebugInfo();

            try {
                const start = products.length;
                const limit = 6;
                const response = await fetch(`http://localhost:3000/products?_start=${start}&_limit=${limit}`);
                const newProducts = await response.json();
                
                console.log(`Loaded ${newProducts.length} products starting from ${start}`);
                
                if (newProducts.length === 0) {
                    hasMore = false;
                } else {
                    products.push(...newProducts);
                    if (newProducts.length < limit) {
                        hasMore = false;
                    }
                }
                
                renderProducts();
            } catch (error) {
                console.error('Error loading products:', error);
            } finally {
                loading = false;
                document.getElementById('loading').style.display = 'none';
                updateDebugInfo();
            }
        }

        function renderProducts() {
            const container = document.getElementById('products-container');
            container.innerHTML = products.map(product => `
                <div class="product">
                    <h3>${product.title}</h3>
                    <p>ID: ${product.id} | Price: $${product.price}</p>
                    <p>${product.description.substring(0, 100)}...</p>
                </div>
            `).join('');
        }

        function reset() {
            products = [];
            loading = false;
            hasMore = true;
            document.getElementById('products-container').innerHTML = '';
            updateDebugInfo();
        }

        // Auto-load on scroll
        window.addEventListener('scroll', () => {
            if (window.innerHeight + window.scrollY >= document.body.offsetHeight - 1000) {
                loadMore();
            }
        });

        // Initial load
        updateDebugInfo();
        loadMore();
    </script>
</body>
</html>
