import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import MainRoutes from "./Routes/MainRoutes";
import Navbar from "./Components/Navbar";
import { asyncCurrentUser } from "./store/Actions/UserAction";
// import { asyncLoadProducts } from "./store/Actions/productAction";

const App = () => {
  const user = useSelector((state) => state.users.data);
  // const products = useSelector((state) => state.products.data);
  
  const dispatch = useDispatch();
  useEffect(() => {
    !user && dispatch(asyncCurrentUser());
    }, [user]);

  // useEffect(() => {
  //   products.length==0 && dispatch(asyncLoadProducts());
  //   }, [products]);

  return (
    <div className="min-h-screen bg-gray-50 text-gray-900">
      {/* Navigation */}
      <Navbar />

      {/* Main Content */}
      <main className="container-responsive min-h-[calc(100vh-80px)] py-4 sm:py-6 lg:py-8">
        <div className="max-w-7xl mx-auto">
          <MainRoutes />
        </div>
      </main>

      {/* Footer Spacer */}
      <div className="h-8 sm:h-12"></div>
    </div>
  );
};

export default App;
