import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import MainRoutes from "./Routes/MainRoutes";
import Navbar from "./Components/Navbar";
import { asyncCurrentUser } from "./store/Actions/UserAction";
// import { asyncLoadProducts } from "./store/Actions/productAction";

const App = () => {
  const user = useSelector((state) => state.users.data);
  // const products = useSelector((state) => state.products.data);
  
  const dispatch = useDispatch();
  useEffect(() => {
    !user && dispatch(asyncCurrentUser());
    }, [user]);

  // useEffect(() => {
  //   products.length==0 && dispatch(asyncLoadProducts());
  //   }, [products]);

  return (
    <div className="min-h-screen bg-gray-50 text-gray-900">
      <div className="container mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 max-w-7xl">
        <Navbar />
        <main className="mt-4 sm:mt-6 md:mt-8">
          <MainRoutes />
        </main>
      </div>
    </div>
  );
};

export default App;
