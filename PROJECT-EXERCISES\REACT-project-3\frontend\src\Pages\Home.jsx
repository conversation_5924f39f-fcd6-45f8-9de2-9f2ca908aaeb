const Home = () => {
  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <section className="text-center py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 text-gray-900">
            Welcome to E-Store
          </h1>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Discover amazing products at great prices. Your one-stop shop for everything you need.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <a 
              href="/products" 
              className="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              Shop Now
            </a>
            <a 
              href="/register" 
              className="px-6 py-3 border border-blue-600 text-blue-600 font-medium rounded-lg hover:bg-blue-50 transition-colors"
            >
              Join Now
            </a>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="grid md:grid-cols-3 gap-8">
        <div className="text-center p-6 bg-white border border-gray-200 rounded-lg">
          <div className="text-3xl mb-3">🚚</div>
          <h3 className="text-lg font-semibold mb-2 text-gray-900">Fast Delivery</h3>
          <p className="text-gray-600 text-sm">Get your orders delivered quickly and safely to your doorstep.</p>
        </div>
        
        <div className="text-center p-6 bg-white border border-gray-200 rounded-lg">
          <div className="text-3xl mb-3">💳</div>
          <h3 className="text-lg font-semibold mb-2 text-gray-900">Secure Payment</h3>
          <p className="text-gray-600 text-sm">Shop with confidence using our secure payment system.</p>
        </div>
        
        <div className="text-center p-6 bg-white border border-gray-200 rounded-lg">
          <div className="text-3xl mb-3">⭐</div>
          <h3 className="text-lg font-semibold mb-2 text-gray-900">Quality Products</h3>
          <p className="text-gray-600 text-sm">We offer only the best quality products from trusted brands.</p>
        </div>
      </section>

      {/* Call to Action */}
      <section className="text-center py-12">
        <div className="max-w-2xl mx-auto">
          <h2 className="text-2xl font-bold mb-3 text-gray-900">
            Ready to Start Shopping?
          </h2>
          <p className="text-gray-600 mb-6">Browse our collection of amazing products and find exactly what you're looking for.</p>
          <a 
            href="/products" 
            className="inline-block px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            View All Products →
          </a>
        </div>
      </section>
    </div>
  )
}

export default Home