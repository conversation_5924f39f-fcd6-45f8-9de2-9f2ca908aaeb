import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { asyncUpdateUser } from "../store/Actions/UserAction";

const ProductTemplate = ({product}) => {
    const dispatch = useDispatch();
  const user = useSelector((state) => state.users.data);

   const addToCarHandler = (id) => {
    // Check if user exists and has a cart property
    if (!user || !user.cart) {
      console.error("User or user.cart is undefined");
      return;
    }

    // Create a deep copy of the user object with the cart
    const copyUser = {
      ...user,
      cart: Array.isArray(user.cart) ? [...user.cart] : [],
    };

    // Find the existing item in the cart
    const existingItemIndex = copyUser.cart.findIndex(
      (c) => c.productid === id
    );

    if (existingItemIndex === -1) {
      copyUser.cart.push({
        productid: id,
        quantity: 1,
      });
    } else {
      copyUser.cart[existingItemIndex] = {
        productid: id,
        quantity: copyUser.cart[existingItemIndex].quantity + 1,
      };
    }

    dispatch(asyncUpdateUser(copyUser.id, copyUser));
  };

  return (
    <div className="w-full sm:w-[48%] lg:w-[30%] mb-6">
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
        {/* Image Container */}
        <div className="relative">
          <img
            className="w-full h-[200px] object-cover"
            src={product.image}
            alt={product.title || 'Product image'}
          />
          
          {/* Category Badge */}
          <div className="absolute top-3 right-3">
            <span className="px-2 py-1 bg-blue-600 text-white text-xs font-medium rounded">
              {product.category}
            </span>
          </div>
        </div>
        
        {/* Content */}
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-2 text-gray-900 line-clamp-2">
            {product.title || 'Product Title'}
          </h3>
          
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
            {product.description ? product.description.slice(0, 80) + '...' : 'No description available'}
          </p>
          
          {/* Price */}
          <div className="mb-4">
            <span className="text-xl font-bold text-gray-900">
              ₹{product.price ? (product.price * 80).toFixed(0) : '0'}
            </span>
            <span className="text-sm text-gray-500 ml-1">INR</span>
          </div>
          
          {/* Action Buttons */}
          <div className="flex gap-2">
            <button 
              onClick={() => addToCarHandler(product.id)}
              className="flex-1 px-3 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors duration-200 text-sm"
            >
              Add to Cart
            </button>
            
            <Link 
              to={`/product/${product.id}`}
              className="px-3 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 transition-colors duration-200 text-sm"
            >
              View Details
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductTemplate;
