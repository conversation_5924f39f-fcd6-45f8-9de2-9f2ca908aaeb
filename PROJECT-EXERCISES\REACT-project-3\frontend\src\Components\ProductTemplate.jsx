import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { asyncUpdateUser } from "../store/Actions/UserAction";

const ProductTemplate = ({product}) => {
    const dispatch = useDispatch();
  const user = useSelector((state) => state.users.data);

   const addToCarHandler = (id) => {
    // Check if user exists and has a cart property
    if (!user || !user.cart) {
      console.error("User or user.cart is undefined");
      return;
    }

    // Create a deep copy of the user object with the cart
    const copyUser = {
      ...user,
      cart: Array.isArray(user.cart) ? [...user.cart] : [],
    };

    // Find the existing item in the cart
    const existingItemIndex = copyUser.cart.findIndex(
      (c) => c.productid === id
    );

    if (existingItemIndex === -1) {
      copyUser.cart.push({
        productid: id,
        quantity: 1,
      });
    } else {
      copyUser.cart[existingItemIndex] = {
        productid: id,
        quantity: copyUser.cart[existingItemIndex].quantity + 1,
      };
    }

    dispatch(asyncUpdateUser(copyUser.id, copyUser));
  };

  return (
    <article className="product-card bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm">
      {/* Image Container */}
      <div className="relative overflow-hidden bg-gray-100">
        <img
          className="product-image w-full h-48 sm:h-52 md:h-56 lg:h-48 xl:h-52 object-cover"
          src={product.image}
          alt={product.title || 'Product image'}
          loading="lazy"
        />

        {/* Category Badge */}
        <div className="absolute top-3 left-3">
          <span className="px-2.5 py-1 bg-blue-600 text-white text-xs font-semibold rounded-full shadow-sm">
            {product.category}
          </span>
        </div>

        {/* Quick View Overlay (appears on hover) */}
        <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 hover:opacity-100">
          <Link
            to={`/product/${product.id}`}
            className="px-4 py-2 bg-white text-gray-900 font-medium rounded-lg shadow-lg transform translate-y-2 hover:translate-y-0 transition-all duration-200"
          >
            Quick View
          </Link>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 sm:p-5 flex flex-col h-full">
        {/* Title */}
        <h3 className="text-base sm:text-lg font-semibold mb-2 text-gray-900 line-clamp-2 min-h-[3rem] sm:min-h-[3.5rem]">
          {product.title || 'Product Title'}
        </h3>

        {/* Description */}
        <p className="text-gray-600 text-sm mb-4 line-clamp-2 flex-grow">
          {product.description ? product.description.slice(0, 100) + '...' : 'No description available'}
        </p>

        {/* Price Section */}
        <div className="mb-4">
          <div className="flex items-baseline gap-2">
            <span className="text-xl sm:text-2xl font-bold text-gray-900">
              ₹{product.price ? (product.price * 80).toFixed(0) : '0'}
            </span>
            <span className="text-sm text-gray-500">INR</span>
          </div>
          {product.price && (
            <div className="flex items-center gap-2 mt-1">
              <span className="text-sm text-gray-400 line-through">
                ₹{(product.price * 80 * 1.2).toFixed(0)}
              </span>
              <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full font-medium">
                17% OFF
              </span>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-2 mt-auto">
          <button
            onClick={() => addToCarHandler(product.id)}
            className="flex-1 btn-primary flex items-center justify-center gap-2 text-sm sm:text-base"
            aria-label={`Add ${product.title} to cart`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9" />
            </svg>
            Add to Cart
          </button>

          <Link
            to={`/product/${product.id}`}
            className="btn-secondary flex items-center justify-center gap-2 text-sm sm:text-base text-center"
            aria-label={`View details for ${product.title}`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            <span className="hidden sm:inline">View Details</span>
            <span className="sm:hidden">Details</span>
          </Link>
        </div>
      </div>
    </article>
  );
};

export default ProductTemplate;
